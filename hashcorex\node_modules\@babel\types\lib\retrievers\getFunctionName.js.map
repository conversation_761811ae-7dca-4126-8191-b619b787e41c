{"version": 3, "names": ["_index", "require", "getNameFromLiteralId", "id", "is<PERSON>ull<PERSON>iteral", "isRegExpLiteral", "pattern", "flags", "isTemplateLiteral", "quasis", "map", "quasi", "value", "raw", "join", "undefined", "String", "getObjectMemberKey", "node", "computed", "isLiteral", "key", "getFunctionName", "parent", "name", "originalNode", "prefix", "isObjectProperty", "isObjectMethod", "isClassMethod", "kind", "isVariableDeclarator", "init", "isAssignmentExpression", "operator", "right", "left", "isIdentifier", "isPrivateName"], "sources": ["../../src/retrievers/getFunctionName.ts"], "sourcesContent": ["import type * as t from \"../index.ts\";\n\nimport {\n  isAssignmentExpression,\n  isClassMethod,\n  isIdentifier,\n  isLiteral,\n  isNullLiteral,\n  isObjectMethod,\n  isObjectProperty,\n  isPrivateName,\n  isRegExpLiteral,\n  isTemplateLiteral,\n  isVariableDeclarator,\n} from \"../validators/generated/index.ts\";\n\nfunction getNameFromLiteralId(id: t.Literal): string {\n  if (isNullLiteral(id)) {\n    return \"null\";\n  }\n\n  if (isRegExpLiteral(id)) {\n    return `/${id.pattern}/${id.flags}`;\n  }\n\n  if (isTemplateLiteral(id)) {\n    return id.quasis.map(quasi => quasi.value.raw).join(\"\");\n  }\n\n  if (id.value !== undefined) {\n    return String(id.value);\n  }\n\n  return null;\n}\n\nfunction getObjectMemberKey(\n  node: t.ObjectProperty | t.ObjectMethod | t.ClassProperty | t.ClassMethod,\n): t.Expression | t.PrivateName {\n  if (!node.computed || isLiteral(node.key)) {\n    return node.key;\n  }\n}\n\ntype GetFunctionNameResult = {\n  name: string;\n  originalNode: t.Node;\n} | null;\n\nexport default function getFunctionName(\n  node: t.ObjectMethod | t.ClassMethod,\n): GetFunctionNameResult;\nexport default function getFunctionName(\n  node: t.Function | t.Class,\n  parent: t.Node,\n): GetFunctionNameResult;\nexport default function getFunctionName(\n  node: t.Function | t.Class,\n  parent?: t.Node,\n): GetFunctionNameResult {\n  if (\"id\" in node && node.id) {\n    return {\n      name: node.id.name,\n      originalNode: node.id,\n    };\n  }\n\n  let prefix = \"\";\n\n  let id;\n  if (isObjectProperty(parent, { value: node })) {\n    // { foo: () => {} };\n    id = getObjectMemberKey(parent);\n  } else if (isObjectMethod(node) || isClassMethod(node)) {\n    // { foo() {} };\n    id = getObjectMemberKey(node);\n    if (node.kind === \"get\") prefix = \"get \";\n    else if (node.kind === \"set\") prefix = \"set \";\n  } else if (isVariableDeclarator(parent, { init: node })) {\n    // let foo = function () {};\n    id = parent.id;\n  } else if (isAssignmentExpression(parent, { operator: \"=\", right: node })) {\n    // foo = function () {};\n    id = parent.left;\n  }\n\n  if (!id) return null;\n\n  const name = isLiteral(id)\n    ? getNameFromLiteralId(id)\n    : isIdentifier(id)\n      ? id.name\n      : isPrivateName(id)\n        ? id.id.name\n        : null;\n  if (name == null) return null;\n\n  return { name: prefix + name, originalNode: id };\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AAcA,SAASC,oBAAoBA,CAACC,EAAa,EAAU;EACnD,IAAI,IAAAC,oBAAa,EAACD,EAAE,CAAC,EAAE;IACrB,OAAO,MAAM;EACf;EAEA,IAAI,IAAAE,sBAAe,EAACF,EAAE,CAAC,EAAE;IACvB,OAAO,IAAIA,EAAE,CAACG,OAAO,IAAIH,EAAE,CAACI,KAAK,EAAE;EACrC;EAEA,IAAI,IAAAC,wBAAiB,EAACL,EAAE,CAAC,EAAE;IACzB,OAAOA,EAAE,CAACM,MAAM,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACzD;EAEA,IAAIX,EAAE,CAACS,KAAK,KAAKG,SAAS,EAAE;IAC1B,OAAOC,MAAM,CAACb,EAAE,CAACS,KAAK,CAAC;EACzB;EAEA,OAAO,IAAI;AACb;AAEA,SAASK,kBAAkBA,CACzBC,IAAyE,EAC3C;EAC9B,IAAI,CAACA,IAAI,CAACC,QAAQ,IAAI,IAAAC,gBAAS,EAACF,IAAI,CAACG,GAAG,CAAC,EAAE;IACzC,OAAOH,IAAI,CAACG,GAAG;EACjB;AACF;AAce,SAASC,eAAeA,CACrCJ,IAA0B,EAC1BK,MAAe,EACQ;EACvB,IAAI,IAAI,IAAIL,IAAI,IAAIA,IAAI,CAACf,EAAE,EAAE;IAC3B,OAAO;MACLqB,IAAI,EAAEN,IAAI,CAACf,EAAE,CAACqB,IAAI;MAClBC,YAAY,EAAEP,IAAI,CAACf;IACrB,CAAC;EACH;EAEA,IAAIuB,MAAM,GAAG,EAAE;EAEf,IAAIvB,EAAE;EACN,IAAI,IAAAwB,uBAAgB,EAACJ,MAAM,EAAE;IAAEX,KAAK,EAAEM;EAAK,CAAC,CAAC,EAAE;IAE7Cf,EAAE,GAAGc,kBAAkB,CAACM,MAAM,CAAC;EACjC,CAAC,MAAM,IAAI,IAAAK,qBAAc,EAACV,IAAI,CAAC,IAAI,IAAAW,oBAAa,EAACX,IAAI,CAAC,EAAE;IAEtDf,EAAE,GAAGc,kBAAkB,CAACC,IAAI,CAAC;IAC7B,IAAIA,IAAI,CAACY,IAAI,KAAK,KAAK,EAAEJ,MAAM,GAAG,MAAM,CAAC,KACpC,IAAIR,IAAI,CAACY,IAAI,KAAK,KAAK,EAAEJ,MAAM,GAAG,MAAM;EAC/C,CAAC,MAAM,IAAI,IAAAK,2BAAoB,EAACR,MAAM,EAAE;IAAES,IAAI,EAAEd;EAAK,CAAC,CAAC,EAAE;IAEvDf,EAAE,GAAGoB,MAAM,CAACpB,EAAE;EAChB,CAAC,MAAM,IAAI,IAAA8B,6BAAsB,EAACV,MAAM,EAAE;IAAEW,QAAQ,EAAE,GAAG;IAAEC,KAAK,EAAEjB;EAAK,CAAC,CAAC,EAAE;IAEzEf,EAAE,GAAGoB,MAAM,CAACa,IAAI;EAClB;EAEA,IAAI,CAACjC,EAAE,EAAE,OAAO,IAAI;EAEpB,MAAMqB,IAAI,GAAG,IAAAJ,gBAAS,EAACjB,EAAE,CAAC,GACtBD,oBAAoB,CAACC,EAAE,CAAC,GACxB,IAAAkC,mBAAY,EAAClC,EAAE,CAAC,GACdA,EAAE,CAACqB,IAAI,GACP,IAAAc,oBAAa,EAACnC,EAAE,CAAC,GACfA,EAAE,CAACA,EAAE,CAACqB,IAAI,GACV,IAAI;EACZ,IAAIA,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;EAE7B,OAAO;IAAEA,IAAI,EAAEE,MAAM,GAAGF,IAAI;IAAEC,YAAY,EAAEtB;EAAG,CAAC;AAClD", "ignoreList": []}